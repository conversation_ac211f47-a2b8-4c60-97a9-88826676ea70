import { app, BrowserWindow, shell, ipcMain, dialog } from 'electron';
import path from 'path';
import url from 'url';
import fs from 'fs';
import isDev from 'electron-is-dev';
import { BoardStateService } from './services/board-state-service'; // New Import
import { AgentStateService } from './services/agent-state-service';
import { LLMService } from './services/llm-service';
import { MCPService } from './services/mcp-service';

// Create a safer console.log that doesn't throw when streams are closed
const safeConsole = {
  log: (...args: any[]) => {
    try {
      console.log(...args);
    } catch (error) {
      // Silently ignore write errors
    }
  },
  error: (...args: any[]) => {
    try {
      console.error(...args);
    } catch (error) {
      // Silently ignore write errors
    }
  }
};

let mainWindow: BrowserWindow | null;
let terminalWindow: BrowserWindow | null = null;
let kanbanWindow: BrowserWindow | null = null;
let agentSystemWindow: BrowserWindow | null = null;
let editorWindow: BrowserWindow | null = null;
let explorerWindow: BrowserWindow | null = null;
let chatWindow: BrowserWindow | null = null;
let timelineWindow: BrowserWindow | null = null;

let boardStateService: BoardStateService | null = null; // New: Declare BoardStateService instance
let agentStateService: AgentStateService | null = null; // New: Declare AgentStateService instance
let llmService: LLMService | null = null; // New: Declare LLMService instance
let mcpService: MCPService | null = null; // New: Declare MCPService instance

function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: false, // Best practice for security
      contextIsolation: true, // Best practice for security
      preload: path.join(__dirname, 'preload.js'), // Enable preload script for IPC
      devTools: true, // Always enable DevTools for debugging
      webSecurity: true, // Enable web security
    },
    icon: path.join(__dirname, '../public/placeholder-logo.svg') // Adjust path if needed
  });

  // Set Content Security Policy - More secure configuration
  mainWindow.webContents.session.webRequest.onHeadersReceived((details, callback) => {
    callback({
      responseHeaders: {
        ...details.responseHeaders,
        'Content-Security-Policy': [
          "default-src 'self'; " +
          "script-src 'self' 'unsafe-eval' 'unsafe-inline' blob: data: https://cdn.jsdelivr.net; " +
          "style-src 'self' 'unsafe-inline' data: https://cdn.jsdelivr.net; " +
          "img-src 'self' data: blob: https:; " +
          "font-src 'self' data: blob: https://cdn.jsdelivr.net; " +
          "connect-src 'self' ws: wss: http: https:; " +
          "worker-src 'self' blob: data: https://cdn.jsdelivr.net; " +
          "child-src 'self' blob: data:; " +
          "frame-src 'self' blob: data:; " +
          "media-src 'self' blob: data:;"
        ]
      }
    });
  });

  // Set a default title
  mainWindow.setTitle('CodeFusion - Modern Code Editor');

  // Determine the correct path to load
  const appPath = app.getAppPath();
  safeConsole.log('App path:', appPath);

  if (isDev || process.argv.includes('--dev')) {
    // In development, load from the Next.js dev server
    safeConsole.log('Loading from dev server');
    mainWindow.loadURL('http://localhost:4444');
    mainWindow.webContents.openDevTools();
  } else {
    // In production, load the statically exported Next.js app
    try {
      // Use a direct path to the index.html file
      const indexFile = path.join(__dirname, '../out/index.html');
      safeConsole.log('Checking for index.html at:', indexFile);

      if (fs.existsSync(indexFile)) {
        safeConsole.log('Found index.html at:', indexFile);
        const indexPath = url.format({
          pathname: indexFile,
          protocol: 'file:',
          slashes: true,
        });

        safeConsole.log('Loading from:', indexPath);
        mainWindow.loadFile(indexFile);
      } else {
        // Try alternative paths
        const possiblePaths = [
          path.join(process.cwd(), 'out/index.html'),
          path.join(app.getAppPath(), 'out/index.html')
        ];

        safeConsole.log('Checking alternative paths:');
        possiblePaths.forEach(p => safeConsole.log(' - ' + p));

        let found = false;
        for (const altPath of possiblePaths) {
          if (fs.existsSync(altPath)) {
            safeConsole.log('Found index.html at:', altPath);
            mainWindow.loadFile(altPath);
            found = true;
            break;
          }
        }

        if (!found) {
          throw new Error('Could not find index.html in any of the expected locations');
        }
      }

      // Only open DevTools in development or when explicitly requested
      if (process.argv.includes('--devtools')) {
        mainWindow.webContents.openDevTools();
      }
    } catch (error) {
      safeConsole.error('Error loading index.html:', error);

      // Show error in window
      mainWindow.loadURL(`data:text/html,<html><body><h1>Error</h1><p>${error}</p></body></html>`);

      // Always open DevTools when there's an error
      mainWindow.webContents.openDevTools();
    }
  }

  // Open external links in the default browser
  // New: Register main window with BoardStateService and AgentStateService
  if (boardStateService && mainWindow) {
    boardStateService.registerWindow(mainWindow);
  }
  if (agentStateService && mainWindow) {
    agentStateService.registerWindow(mainWindow);
  }

  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url);
    return { action: 'deny' };
  });

  mainWindow.on('closed', () => {
    mainWindow = null;
  });
}

// Function to create any generic window and register with BoardStateService
function createGenericWindow(
  existingWindowVar: BrowserWindow | null,
  options: Electron.BrowserWindowConstructorOptions,
  loadUrl: string,
  onClosedCallback: () => void
): BrowserWindow {
  if (existingWindowVar) {
    existingWindowVar.focus();
    return existingWindowVar;
  }

  const newWindow = new BrowserWindow({
    ...options, // Spread default/passed options
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js'),
      devTools: true,
      webSecurity: true,
      ...options.webPreferences, // Allow override
    },
    icon: options.icon || path.join(__dirname, '../public/placeholder-logo.svg')
  });

  newWindow.loadURL(loadUrl);

  newWindow.once('ready-to-show', () => {
    newWindow?.show();
    if (process.argv.includes('--devtools')) {
      newWindow?.webContents.openDevTools();
    }
  });

  newWindow.on('closed', onClosedCallback);

  // New: Register the new window with BoardStateService and AgentStateService
  if (boardStateService) {
    boardStateService.registerWindow(newWindow);
  }
  if (agentStateService) {
    agentStateService.registerWindow(newWindow);
  }
  return newWindow;
}

function createTerminalWindow() {
  const terminalUrl = isDev || process.argv.includes('--dev')
    ? 'http://localhost:4444/terminal'
    : url.format({
        pathname: path.join(__dirname, '../out/terminal/index.html'),
        protocol: 'file:',
        slashes: true,
      });
  terminalWindow = createGenericWindow(
    terminalWindow,
    { width: 800, height: 600, minWidth: 400, minHeight: 300, frame: true, show: false, title: 'Terminal - CodeFusion' },
    terminalUrl,
    () => { terminalWindow = null; }
  );
}

function createKanbanWindow(boardId: string) {
  const kanbanUrl = isDev || process.argv.includes('--dev')
    ? `http://localhost:4444/kanban/${boardId}`
    : url.format({
        pathname: path.join(__dirname, `../out/kanban/${boardId}/index.html`),
        protocol: 'file:',
        slashes: true,
      });
  kanbanWindow = createGenericWindow(
    kanbanWindow,
    { width: 1200, height: 800, minWidth: 800, minHeight: 600, frame: true, show: false, title: 'Kanban Board - CodeFusion' },
    kanbanUrl,
    () => { kanbanWindow = null; }
  );
}

function createAgentSystemWindow() {
  if (agentSystemWindow) {
    agentSystemWindow.focus();
    return;
  }

  agentSystemWindow = new BrowserWindow({
    width: 1000,
    height: 700,
    minWidth: 600,
    minHeight: 400,
    frame: true,
    show: false,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js'),
      devTools: true,
      webSecurity: true,
    },
    title: 'Agent System - CodeFusion',
    icon: path.join(__dirname, '../public/placeholder-logo.svg')
  });

  // Load the dedicated agent system route
  const agentUrl = isDev || process.argv.includes('--dev')
    ? 'http://localhost:4444/agent-system'
    : url.format({
        pathname: path.join(__dirname, '../out/agent-system/index.html'),
        protocol: 'file:',
        slashes: true,
      });

  agentSystemWindow.loadURL(agentUrl);

  agentSystemWindow.once('ready-to-show', () => {
    agentSystemWindow?.show();
    if (process.argv.includes('--devtools')) {
      agentSystemWindow?.webContents.openDevTools();
    }
  });

  agentSystemWindow.on('closed', () => {
    agentSystemWindow = null;
  });

  // Register agent system window with AgentStateService
  if (agentStateService) {
    agentStateService.registerWindow(agentSystemWindow);
  }
}

function createEditorWindow(filePath?: string) {
  if (editorWindow) {
    editorWindow.focus();
    return;
  }

  editorWindow = new BrowserWindow({
    width: 1000,
    height: 700,
    minWidth: 600,
    minHeight: 400,
    frame: true,
    show: false,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js'),
      devTools: true,
      webSecurity: true,
    },
    title: 'Editor - CodeFusion',
    icon: path.join(__dirname, '../public/placeholder-logo.svg')
  });

  // Load the dedicated editor route
  const editorUrl = isDev || process.argv.includes('--dev')
    ? `http://localhost:4444/editor${filePath ? `?file=${encodeURIComponent(filePath)}` : ''}`
    : url.format({
        pathname: path.join(__dirname, '../out/editor/index.html'),
        protocol: 'file:',
        slashes: true,
      });

  editorWindow.loadURL(editorUrl);

  editorWindow.once('ready-to-show', () => {
    editorWindow?.show();
    if (process.argv.includes('--devtools')) {
      editorWindow?.webContents.openDevTools();
    }
  });

  editorWindow.on('closed', () => {
    editorWindow = null;
  });
}

function createExplorerWindow() {
  if (explorerWindow) {
    explorerWindow.focus();
    return;
  }

  explorerWindow = new BrowserWindow({
    width: 400,
    height: 600,
    minWidth: 300,
    minHeight: 400,
    frame: true,
    show: false,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js'),
      devTools: true,
      webSecurity: true,
    },
    title: 'Explorer - CodeFusion',
    icon: path.join(__dirname, '../public/placeholder-logo.svg')
  });

  // Load the dedicated explorer route
  const explorerUrl = isDev || process.argv.includes('--dev')
    ? 'http://localhost:4444/explorer'
    : url.format({
        pathname: path.join(__dirname, '../out/explorer/index.html'),
        protocol: 'file:',
        slashes: true,
      });

  explorerWindow.loadURL(explorerUrl);

  explorerWindow.once('ready-to-show', () => {
    explorerWindow?.show();
    if (process.argv.includes('--devtools')) {
      explorerWindow?.webContents.openDevTools();
    }
  });

  explorerWindow.on('closed', () => {
    explorerWindow = null;
  });
}

function createChatWindow() {
  if (chatWindow) {
    chatWindow.focus();
    return;
  }

  chatWindow = new BrowserWindow({
    width: 400,
    height: 600,
    minWidth: 300,
    minHeight: 400,
    frame: true,
    show: false,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js'),
      devTools: true,
      webSecurity: true,
    },
    title: 'AI Chat - CodeFusion',
    icon: path.join(__dirname, '../public/placeholder-logo.svg')
  });

  // Load the dedicated chat route
  const chatUrl = isDev || process.argv.includes('--dev')
    ? 'http://localhost:4444/chat'
    : url.format({
        pathname: path.join(__dirname, '../out/chat/index.html'),
        protocol: 'file:',
        slashes: true,
      });

  chatWindow.loadURL(chatUrl);

  chatWindow.once('ready-to-show', () => {
    chatWindow?.show();
    if (process.argv.includes('--devtools')) {
      chatWindow?.webContents.openDevTools();
    }
  });

  chatWindow.on('closed', () => {
    chatWindow = null;
  });
}

function createTimelineWindow() {
  if (timelineWindow) {
    timelineWindow.focus();
    return;
  }

  timelineWindow = new BrowserWindow({
    width: 1000,
    height: 700,
    minWidth: 800,
    minHeight: 500,
    frame: true,
    show: false,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js'),
      devTools: true,
      webSecurity: true,
    },
    title: 'Task Timeline Inspector - CodeFusion',
    icon: path.join(__dirname, '../public/placeholder-logo.svg')
  });

  // Load the dedicated timeline route
  const timelineUrl = isDev || process.argv.includes('--dev')
    ? 'http://localhost:4444/timeline'
    : url.format({
        pathname: path.join(__dirname, '../out/timeline/index.html'),
        protocol: 'file:',
        slashes: true,
      });

  timelineWindow.loadURL(timelineUrl);

  timelineWindow.once('ready-to-show', () => {
    timelineWindow?.show();
    if (process.argv.includes('--devtools')) {
      timelineWindow?.webContents.openDevTools();
    }
  });

  timelineWindow.on('closed', () => {
    timelineWindow = null;
  });
}

app.on('ready', () => {
  // New: Initialize BoardStateService, AgentStateService, LLMService, and MCPService
  if (!boardStateService) {
    boardStateService = new BoardStateService();
  }
  if (!agentStateService) {
    agentStateService = new AgentStateService();
  }
  if (!llmService) {
    llmService = new LLMService();
  }
  if (!mcpService) {
    mcpService = new MCPService();
  }

  createWindow();
  // Register IPC handlers after main window is ready
  ipcMain.on('open-terminal-window', () => {
    createTerminalWindow();
  });
  ipcMain.on('open-kanban-window', (event, boardId: string) => {
    createKanbanWindow(boardId);
  });
  ipcMain.on('open-agent-system-window', () => {
    createAgentSystemWindow();
  });
  ipcMain.on('open-editor-window', (event, filePath?: string) => {
    createEditorWindow(filePath);
  });
  ipcMain.on('open-explorer-window', () => {
    createExplorerWindow();
  });
  ipcMain.on('open-chat-window', () => {
    createChatWindow();
  });
  ipcMain.on('open-timeline-window', () => {
    createTimelineWindow();
  });

  // Register file sync IPC handlers for real-time synchronization
  ipcMain.on('file-sync-event', (event, syncEvent) => {
    // Broadcast file sync events to all windows except the sender
    const allWindows = BrowserWindow.getAllWindows();
    allWindows.forEach(window => {
      if (window.webContents !== event.sender) {
        window.webContents.send('file-sync-event', syncEvent);
      }
    });
  });

  // Register editor state IPC handlers for cross-window synchronization
  ipcMain.on('editor-state-event', (event, editorStateEvent) => {
    // Broadcast editor state events to all windows except the sender
    const allWindows = BrowserWindow.getAllWindows();
    allWindows.forEach(window => {
      if (window.webContents !== event.sender) {
        window.webContents.send('editor-state-event', editorStateEvent);
      }
    });
  });

  // Handle initial state requests from new windows
  ipcMain.on('request-initial-editor-state', (event, requestData) => {
    // Broadcast the request to all windows except the sender
    const allWindows = BrowserWindow.getAllWindows();
    allWindows.forEach(window => {
      if (window.webContents !== event.sender) {
        window.webContents.send('request-initial-editor-state', requestData);
      }
    });
  });

  // ✅ Auto-Save IPC Handlers
  ipcMain.handle('save-agent-states', async (event, agentStates) => {
    try {
      if (agentStateService) {
        await agentStateService.saveAgentStates(agentStates);
        return { success: true };
      }
      return { success: false, error: 'Agent state service not available' };
    } catch (error) {
      safeConsole.error('Failed to save agent states:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  });

  ipcMain.handle('save-board-state', async (event, boardState) => {
    try {
      if (boardStateService) {
        await boardStateService.saveBoardState(boardState);
        return { success: true };
      }
      return { success: false, error: 'Board state service not available' };
    } catch (error) {
      safeConsole.error('Failed to save board state:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  });

  // ✅ Note: save-file handler is registered later in the file system operations section

  // Register editor action state IPC handlers for cross-window synchronization
  ipcMain.on('editor-action-event', (event, actionEvent) => {
    // Broadcast action events to all windows except the sender
    const allWindows = BrowserWindow.getAllWindows();
    allWindows.forEach(window => {
      if (window.webContents !== event.sender) {
        window.webContents.send('editor-action-event', actionEvent);
      }
    });
  });

  // ✅ Register chat state IPC handlers for real-time synchronization
  ipcMain.on('chat-state-update', (event, chatState) => {
    // Broadcast chat state to all windows except the sender
    const allWindows = BrowserWindow.getAllWindows();
    allWindows.forEach(window => {
      if (window.webContents !== event.sender) {
        window.webContents.send('chat-state-update', chatState);
      }
    });
  });

  ipcMain.on('chat-message-added', (event, message) => {
    // Broadcast new message to all windows except the sender
    const allWindows = BrowserWindow.getAllWindows();
    allWindows.forEach(window => {
      if (window.webContents !== event.sender) {
        window.webContents.send('chat-message-added', message);
      }
    });
  });

  ipcMain.on('chat-processing-changed', (event, isProcessing, streamingMessageId) => {
    // Broadcast processing state to all windows except the sender
    const allWindows = BrowserWindow.getAllWindows();
    allWindows.forEach(window => {
      if (window.webContents !== event.sender) {
        window.webContents.send('chat-processing-changed', isProcessing, streamingMessageId);
      }
    });
  });

  ipcMain.on('chat-message-updated', (event, messageId, updates) => {
    // Broadcast message update to all windows except the sender
    const allWindows = BrowserWindow.getAllWindows();
    allWindows.forEach(window => {
      if (window.webContents !== event.sender) {
        window.webContents.send('chat-message-updated', messageId, updates);
      }
    });
  });
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (mainWindow === null) {
    createWindow();
  }
  // Also reactivate floating windows if they were minimized/hidden by activate event
  if (terminalWindow) {
    terminalWindow.show();
  }
  if (kanbanWindow) {
    kanbanWindow.show();
  }
  if (agentSystemWindow) {
    agentSystemWindow.show();
  }
  if (editorWindow) {
    editorWindow.show();
  }
  if (explorerWindow) {
    explorerWindow.show();
  }
  if (chatWindow) {
    chatWindow.show();
  }
  if (timelineWindow) {
    timelineWindow.show();
  }
});

// IPC handlers for file system operations
ipcMain.handle('select-folder', async () => {
  const result = await dialog.showOpenDialog(mainWindow!, {
    properties: ['openDirectory'],
    title: 'Select Project Folder'
  });

  if (!result.canceled && result.filePaths.length > 0) {
    const folderPath = result.filePaths[0];
    return {
      success: true,
      path: folderPath,
      name: path.basename(folderPath)
    };
  }

  return { success: false };
});

ipcMain.handle('read-directory', async (event, dirPath: string) => {
  try {
    const items = fs.readdirSync(dirPath, { withFileTypes: true });
    const result = [];

    for (const item of items) {
      const itemPath = path.join(dirPath, item.name);
      const stats = fs.statSync(itemPath);

      if (item.isDirectory()) {
        result.push({
          id: Date.now() + Math.random(),
          name: item.name,
          type: 'folder',
          path: itemPath,
          expanded: false,
          files: []
        });
      } else {
        const ext = path.extname(item.name).slice(1);
        result.push({
          id: Date.now() + Math.random(),
          name: item.name,
          type: ext || 'file',
          path: itemPath,
          size: stats.size,
          modified: stats.mtime
        });
      }
    }

    return { success: true, items: result };
  } catch (error) {
    safeConsole.error('Error reading directory:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
});

ipcMain.handle('read-file', async (event, filePath: string) => {
  try {
    const content = fs.readFileSync(filePath, 'utf-8');
    return { success: true, content };
  } catch (error) {
    safeConsole.error('Error reading file:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
});

// ✅ Task 78: Taskmaster Task Sync IPC Handlers
ipcMain.handle('taskmaster:update-task', async (event, taskId: string, updatedFields: any) => {
  try {
    safeConsole.log(`Updating Taskmaster task ${taskId}:`, updatedFields);

    // This is a placeholder implementation
    // In a real implementation, you would:
    // 1. Load the tasks.json file
    // 2. Find the task by ID
    // 3. Update the task with new fields
    // 4. Save the file back

    return { success: true, taskId, updatedFields };
  } catch (error) {
    safeConsole.error('Error updating Taskmaster task:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
});

ipcMain.handle('taskmaster:backup-tasks', async (event, projectPath: string) => {
  try {
    const tasksFilePath = path.join(projectPath, '.taskmaster', 'tasks.json');
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupPath = path.join(projectPath, '.taskmaster', `tasks.backup.${timestamp}.json`);

    if (fs.existsSync(tasksFilePath)) {
      fs.copyFileSync(tasksFilePath, backupPath);
      safeConsole.log(`Created Taskmaster backup: ${backupPath}`);
      return { success: true, backupPath };
    } else {
      return { success: false, error: 'Tasks file not found' };
    }
  } catch (error) {
    safeConsole.error('Error creating Taskmaster backup:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
});

ipcMain.handle('taskmaster:validate-tasks', async (event, filePath: string) => {
  try {
    if (!fs.existsSync(filePath)) {
      return { success: false, error: 'File does not exist' };
    }

    const content = fs.readFileSync(filePath, 'utf-8');
    const data = JSON.parse(content);

    // Basic validation
    if (!data.tasks || !Array.isArray(data.tasks)) {
      return { success: false, error: 'Invalid tasks structure' };
    }

    return { success: true, taskCount: data.tasks.length };
  } catch (error) {
    safeConsole.error('Error validating Taskmaster tasks:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
});

ipcMain.handle('taskmaster:sync-status', async (event, taskId: string, status: string, metadata: any) => {
  try {
    safeConsole.log(`Syncing task status for ${taskId}: ${status}`, metadata);

    // This is a placeholder implementation
    // In a real implementation, you would update the specific task's status

    return { success: true, taskId, status, metadata };
  } catch (error) {
    safeConsole.error('Error syncing task status:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
});

ipcMain.handle('save-file', async (event, filePath: string, content: string) => {
  try {
    fs.writeFileSync(filePath, content, 'utf-8');

    // Broadcast save success to all windows
    const allWindows = BrowserWindow.getAllWindows();
    allWindows.forEach(window => {
      window.webContents.send('file-saved', {
        filePath,
        content,
        timestamp: Date.now()
      });
    });

    return { success: true };
  } catch (error) {
    safeConsole.error('Error saving file:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
});

ipcMain.handle('create-file', async (event, filePath: string, content: string = '') => {
  try {
    // Check if file already exists
    if (fs.existsSync(filePath)) {
      return { success: false, error: 'File already exists' };
    }

    // Create directory if it doesn't exist
    const dir = path.dirname(filePath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    fs.writeFileSync(filePath, content, 'utf-8');
    return { success: true };
  } catch (error) {
    safeConsole.error('Error creating file:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
});

ipcMain.handle('delete-file', async (event, filePath: string) => {
  try {
    const stats = fs.statSync(filePath);
    if (stats.isDirectory()) {
      fs.rmSync(filePath, { recursive: true, force: true });
    } else {
      fs.unlinkSync(filePath);
    }
    return { success: true };
  } catch (error) {
    safeConsole.error('Error deleting file:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
});

// ✅ Command execution handler for PRD parsing and other CLI operations
ipcMain.handle('execute-command', async (event, command: string, workingDirectory?: string) => {
  const { spawn } = require('child_process');

  try {
    safeConsole.log(`Executing command: ${command} in ${workingDirectory || process.cwd()}`);

    return new Promise((resolve) => {
      const cwd = workingDirectory || process.cwd();

      // Parse command and arguments
      const parts = command.split(' ');
      const cmd = parts[0];
      const args = parts.slice(1);

      const childProcess = spawn(cmd, args, {
        cwd,
        stdio: ['pipe', 'pipe', 'pipe'],
        shell: true
      });

      let stdout = '';
      let stderr = '';

      childProcess.stdout?.on('data', (data: Buffer) => {
        stdout += data.toString();
      });

      childProcess.stderr?.on('data', (data: Buffer) => {
        stderr += data.toString();
      });

      childProcess.on('close', (code: number | null) => {
        const success = code === 0;
        const output = stdout || stderr;

        safeConsole.log(`Command completed with code ${code}`);
        if (stdout) safeConsole.log('STDOUT:', stdout);
        if (stderr) safeConsole.log('STDERR:', stderr);

        resolve({
          success,
          output,
          error: success ? null : stderr || `Command failed with exit code ${code}`,
          exitCode: code || -1
        });
      });

      childProcess.on('error', (error: Error) => {
        safeConsole.error('Command execution error:', error);
        resolve({
          success: false,
          output: '',
          error: error.message,
          exitCode: -1
        });
      });

      // Set timeout for long-running commands
      setTimeout(() => {
        if (!childProcess.killed) {
          childProcess.kill();
          resolve({
            success: false,
            output: stdout,
            error: 'Command timed out after 30 seconds',
            exitCode: -1
          });
        }
      }, 30000);
    });
  } catch (error) {
    safeConsole.error('Error executing command:', error);
    return {
      success: false,
      output: '',
      error: error instanceof Error ? error.message : 'Unknown error',
      exitCode: -1
    };
  }
});

// ✅ MCP Protocol IPC Handlers
ipcMain.handle('mcp:initializeConnection', async (event, serverId: string, config: any) => {
  if (!mcpService) {
    return {
      success: false,
      error: 'MCP service not initialized'
    };
  }

  try {
    const result = await mcpService.initializeConnection(serverId, {
      command: config.command,
      args: config.args || [],
      timeout: config.timeout || 30000,
      maxRetries: config.maxRetries || 3
    });

    return result;
  } catch (error) {
    safeConsole.error('MCP connection error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown MCP connection error'
    };
  }
});

ipcMain.handle('mcp:sendTask', async (event, serverId: string, request: any) => {
  if (!mcpService) {
    return {
      success: false,
      error: 'MCP service not initialized'
    };
  }

  try {
    const result = await mcpService.sendTask(serverId, request);
    return result;
  } catch (error) {
    safeConsole.error('MCP task error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown MCP task error'
    };
  }
});

ipcMain.handle('mcp:syncAgentState', async (event, serverId: string, agentId: string, state: any) => {
  if (!mcpService) {
    return {
      success: false,
      error: 'MCP service not initialized'
    };
  }

  try {
    const result = await mcpService.syncAgentState(serverId, agentId, state);
    return result;
  } catch (error) {
    safeConsole.error('MCP state sync error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown MCP state sync error'
    };
  }
});

ipcMain.handle('mcp:testConnection', async (event, serverId: string) => {
  if (!mcpService) {
    return {
      success: false,
      error: 'MCP service not initialized'
    };
  }

  try {
    const result = await mcpService.testConnection(serverId);
    return result;
  } catch (error) {
    safeConsole.error('MCP test error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown MCP test error'
    };
  }
});

ipcMain.handle('mcp:disconnectServer', async (event, serverId: string) => {
  if (!mcpService) {
    return {
      success: false,
      error: 'MCP service not initialized'
    };
  }

  try {
    const result = await mcpService.disconnectServer(serverId);
    return result;
  } catch (error) {
    safeConsole.error('MCP disconnect error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown MCP disconnect error'
    };
  }
});

ipcMain.handle('mcp:getConnectedServers', async (event) => {
  if (!mcpService) {
    return [];
  }

  try {
    return mcpService.getConnectedServers();
  } catch (error) {
    safeConsole.error('MCP get servers error:', error);
    return [];
  }
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (mainWindow === null) {
    createWindow();
  }
});

// Cleanup MCP connections on app quit
app.on('before-quit', async () => {
  if (mcpService) {
    await mcpService.disconnectAll();
  }
});