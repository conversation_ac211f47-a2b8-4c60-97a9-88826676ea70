"use client"

import type React from "react"

import { useState, useRef, useEffect, useCallback } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Plus, X, RefreshCw, Trash2, Command } from "lucide-react"
import { cn } from "@/lib/utils"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { useTheme } from "next-themes"

// ✅ Task 82: Import xterm.js for real terminal emulation
import { Terminal } from 'xterm'
import { FitAddon } from 'xterm-addon-fit'
import { WebLinksAddon } from 'xterm-addon-web-links'

// ✅ Task 82: Real terminal session interface
interface RealTerminalSession {
  id: string
  name: string
  sessionId: string
  terminal: Terminal
  fitAddon: FitAddon
  shellType: "bash" | "powershell" | "cmd" | "zsh"
  isConnected: boolean
}

export default function TerminalManager({ isRunning }: { isRunning: boolean }) {
  const [terminals, setTerminals] = useState<RealTerminalSession[]>([])
  const [activeTerminalId, setActiveTerminalId] = useState<string>("")
  const terminalRefs = useRef<Map<string, HTMLDivElement>>(new Map())
  const { theme } = useTheme()
  const isDark = theme === "dark"

  // ✅ Task 82: Terminal API access
  const terminalAPI = typeof window !== 'undefined' && (window as any).electronAPI?.terminalAPI

  // ✅ Task 82: Create real terminal session
  const createTerminalSession = useCallback(async (shellType: "bash" | "powershell" | "cmd" | "zsh" = "bash") => {
    if (!terminalAPI) {
      console.error('Terminal API not available')
      return
    }

    const id = `terminal-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`

    try {
      // Create xterm.js terminal instance
      const terminal = new Terminal({
        theme: {
          background: isDark ? '#1e1e1e' : '#ffffff',
          foreground: isDark ? '#ffffff' : '#000000',
          cursor: isDark ? '#ffffff' : '#000000',
          selection: isDark ? '#ffffff40' : '#00000040'
        },
        fontFamily: 'Monaco, Menlo, "Ubuntu Mono", Consolas, monospace',
        fontSize: 14,
        lineHeight: 1.2,
        cursorBlink: true,
        cursorStyle: 'block',
        scrollback: 1000,
        tabStopWidth: 4
      })

      // Add addons
      const fitAddon = new FitAddon()
      const webLinksAddon = new WebLinksAddon()

      terminal.loadAddon(fitAddon)
      terminal.loadAddon(webLinksAddon)

      // Start backend terminal session
      const result = await terminalAPI.startTerminal(80, 24, id)

      if (!result.success) {
        console.error('Failed to start terminal session:', result.error)
        return
      }

      const sessionId = result.sessionId

      // Create session object
      const session: RealTerminalSession = {
        id,
        name: `${shellType}-${terminals.length + 1}`,
        sessionId,
        terminal,
        fitAddon,
        shellType,
        isConnected: true
      }

      // Set up terminal data handler
      const dataCleanup = terminalAPI.onTerminalData((receivedSessionId: string, data: string) => {
        if (receivedSessionId === sessionId) {
          terminal.write(data)
        }
      })

      // Set up terminal exit handler
      const exitCleanup = terminalAPI.onTerminalExit((receivedSessionId: string, exitCode: number) => {
        if (receivedSessionId === sessionId) {
          console.log(`Terminal session ${sessionId} exited with code ${exitCode}`)
          setTerminals(prev => prev.map(t =>
            t.sessionId === sessionId ? { ...t, isConnected: false } : t
          ))
        }
      })

      // Handle user input
      terminal.onData((data: string) => {
        if (session.isConnected) {
          terminalAPI.writeToTerminal(sessionId, data)
        }
      })

      // Store cleanup functions
      ;(session as any).dataCleanup = dataCleanup
      ;(session as any).exitCleanup = exitCleanup

      // Add to terminals list
      setTerminals(prev => [...prev, session])
      setActiveTerminalId(id)

      console.log(`Created terminal session ${id} with backend session ${sessionId}`)

    } catch (error) {
      console.error('Error creating terminal session:', error)
    }
  }, [terminalAPI, isDark, terminals.length])

  // ✅ Task 82: Close terminal session
  const closeTerminal = useCallback(async (id: string, e?: React.MouseEvent) => {
    e?.stopPropagation()

    const terminal = terminals.find(t => t.id === id)
    if (!terminal) return

    try {
      // Close backend session
      if (terminalAPI && terminal.isConnected) {
        terminalAPI.closeTerminal(terminal.sessionId)
      }

      // Cleanup event listeners
      if ((terminal as any).dataCleanup) {
        (terminal as any).dataCleanup()
      }
      if ((terminal as any).exitCleanup) {
        (terminal as any).exitCleanup()
      }

      // Dispose xterm.js terminal
      terminal.terminal.dispose()

      // Remove from terminals list
      setTerminals(prev => prev.filter(t => t.id !== id))

      // If this was the active terminal, switch to another one
      if (activeTerminalId === id) {
        const remainingTerminals = terminals.filter(t => t.id !== id)
        if (remainingTerminals.length > 0) {
          setActiveTerminalId(remainingTerminals[0].id)
        } else {
          setActiveTerminalId("")
        }
      }

      console.log(`Closed terminal session ${id}`)
    } catch (error) {
      console.error('Error closing terminal:', error)
    }
  }, [terminals, activeTerminalId, terminalAPI])

  // ✅ Task 82: Mount terminal to DOM element
  useEffect(() => {
    terminals.forEach(terminal => {
      const element = terminalRefs.current.get(terminal.id)
      if (element && !element.hasChildNodes()) {
        terminal.terminal.open(element)
        terminal.fitAddon.fit()

        // Focus the terminal if it's active
        if (terminal.id === activeTerminalId) {
          terminal.terminal.focus()
        }
      }
    })
  }, [terminals, activeTerminalId])

  // ✅ Task 82: Handle window resize
  useEffect(() => {
    const handleResize = () => {
      terminals.forEach(terminal => {
        if (terminal.isConnected) {
          terminal.fitAddon.fit()
          const { cols, rows } = terminal.terminal
          if (terminalAPI) {
            terminalAPI.resizeTerminal(terminal.sessionId, cols, rows)
          }
        }
      })
    }

    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [terminals, terminalAPI])

  // ✅ Task 82: Create initial terminal if none exist
  useEffect(() => {
    if (terminals.length === 0 && terminalAPI) {
      createTerminalSession('bash')
    }
  }, [terminals.length, terminalAPI, createTerminalSession])

  // Get the active terminal
  const activeTerminal = terminals.find(t => t.id === activeTerminalId)

  return (
    <div className="flex flex-col h-full">
      {/* Terminal tabs */}
      <div className="flex items-center border-b border-editor-border bg-editor-sidebar-bg overflow-x-auto hide-scrollbar">
        {terminals.map((terminal) => (
          <div
            key={terminal.id}
            className={cn(
              "flex items-center h-8 px-3 border-r border-editor-border cursor-pointer group",
              terminal.id === activeTerminalId
                ? "bg-editor-terminal-bg text-foreground"
                : "bg-editor-sidebar-bg text-muted-foreground hover:bg-editor-terminal-bg/50",
            )}
            onClick={(e) => {
              e.stopPropagation()
              setActiveTerminalId(terminal.id)
            }}
          >
            <span className="text-xs">{terminal.name}</span>
            <span className={cn(
              "ml-2 w-2 h-2 rounded-full",
              terminal.isConnected ? "bg-green-500" : "bg-red-500"
            )} />
            <Button
              variant="ghost"
              size="icon"
              className="h-5 w-5 ml-2 opacity-0 group-hover:opacity-100"
              onClick={(e) => closeTerminal(terminal.id, e)}
            >
              <X className="h-3 w-3" />
            </Button>
          </div>
        ))}

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon" className="h-8 w-8 text-muted-foreground hover:text-foreground">
              <Plus className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start">
            <DropdownMenuItem onClick={() => createTerminalSession("bash")}>
              <Command className="mr-2 h-4 w-4" />
              <span>Bash</span>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => createTerminalSession("powershell")}>
              <Command className="mr-2 h-4 w-4" />
              <span>PowerShell</span>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => createTerminalSession("cmd")}>
              <Command className="mr-2 h-4 w-4" />
              <span>Command Prompt</span>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => createTerminalSession("zsh")}>
              <Command className="mr-2 h-4 w-4" />
              <span>Zsh</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* ✅ Task 82: Real terminal content area */}
      <div className="flex-1 relative">
        {terminals.map((terminal) => (
          <div
            key={terminal.id}
            ref={(el) => {
              if (el) {
                terminalRefs.current.set(terminal.id, el)
              } else {
                terminalRefs.current.delete(terminal.id)
              }
            }}
            className={cn(
              "absolute inset-0 bg-editor-terminal-bg",
              terminal.id === activeTerminalId ? "block" : "hidden"
            )}
            style={{
              fontFamily: 'Monaco, Menlo, "Ubuntu Mono", Consolas, monospace'
            }}
          />
        ))}

        {/* Show message when no terminals or terminal API not available */}
        {(terminals.length === 0 || !terminalAPI) && (
          <div className="flex items-center justify-center h-full text-muted-foreground">
            <div className="text-center">
              <Command className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm">
                {!terminalAPI ? 'Terminal API not available' : 'No terminal sessions'}
              </p>
              {terminalAPI && (
                <Button
                  variant="outline"
                  size="sm"
                  className="mt-2"
                  onClick={() => createTerminalSession('bash')}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Create Terminal
                </Button>
              )}
            </div>
          </div>
        )}

        {/* Running indicator */}
        {isRunning && (
          <div className="absolute top-2 right-2 flex items-center bg-green-500/10 text-green-500 px-2 py-1 rounded text-xs">
            <RefreshCw className="h-3 w-3 mr-1 animate-spin" />
            Running
          </div>
        )}
      </div>

      {/* Terminal status bar */}
      {activeTerminal && (
        <div className="h-6 border-t border-editor-border bg-editor-statusbar-bg flex items-center px-2 text-xs text-muted-foreground">
          <span>{activeTerminal.shellType}</span>
          <span className="mx-2">•</span>
          <span className={cn(
            "flex items-center",
            activeTerminal.isConnected ? "text-green-500" : "text-red-500"
          )}>
            <span className={cn(
              "w-2 h-2 rounded-full mr-1",
              activeTerminal.isConnected ? "bg-green-500" : "bg-red-500"
            )} />
            {activeTerminal.isConnected ? "Connected" : "Disconnected"}
          </span>
          <span className="mx-2">•</span>
          <span>Session: {activeTerminal.sessionId}</span>
        </div>
      )}
    </div>
  )
}
